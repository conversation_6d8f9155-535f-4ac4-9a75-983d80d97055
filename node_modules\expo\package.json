{"name": "expo", "version": "52.0.0", "description": "The Expo SDK", "main": "src/Expo.ts", "module": "src/Expo.ts", "types": "build/Expo.d.ts", "sideEffects": ["*.fx.tsx", "*.fx.web.tsx", "./src/winter/*.ts"], "bin": "bin/cli", "files": ["android", "bin", "build", "ios", "scripts", "src", "AppEntry.js", "Expo.podspec", "bundledNativeModules.json", "expo-module.config.json", "metro-config.js", "metro-config.d.ts", "dom", "config.js", "config.d.ts", "config-plugins.js", "config-plugins.d.ts", "devtools.js", "devtools.d.ts", "fetch.js", "fetch.d.ts", "fingerprint.js", "fingerprint.d.ts", "react-native.config.js", "requiresExtraSetup.json", "tsconfig.base.json", "types"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "jest": {"preset": "expo-module-scripts"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo"}, "keywords": ["expo"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/expo", "dependencies": {"@babel/runtime": "^7.20.0", "@expo/cli": "0.20.4", "@expo/config": "10.0.2", "@expo/config-plugins": "9.0.7", "@expo/fingerprint": "0.11.2", "@expo/metro-config": "0.19.0", "@expo/vector-icons": "^14.0.0", "babel-preset-expo": "~12.0.0-preview.6", "expo-asset": "~11.0.1", "expo-constants": "~17.0.2", "expo-file-system": "~18.0.1", "expo-font": "~13.0.1", "expo-keep-awake": "~14.0.1", "expo-modules-autolinking": "2.0.0-preview.3", "expo-modules-core": "2.0.0", "fbemitter": "^3.0.0", "web-streams-polyfill": "^3.3.2", "whatwg-url-without-unicode": "8.0.0-3"}, "devDependencies": {"@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "expo-module-scripts": "^4.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.1"}, "peerDependencies": {"@expo/dom-webview": "*", "@expo/metro-runtime": "*", "react": "*", "react-native": "*", "react-native-webview": "*"}, "peerDependenciesMeta": {"@expo/dom-webview": {"optional": true}, "@expo/metro-runtime": {"optional": true}, "react-native-webview": {"optional": true}}, "gitHead": "1f84b4c952ed9785c41ee65c9877e44f1df27af9"}