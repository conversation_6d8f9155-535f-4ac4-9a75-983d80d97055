{"name": "expo-keep-awake", "version": "14.0.3", "description": "Provides a React component that prevents the screen sleeping when rendered. It also exposes static methods to control the behavior imperatively.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "awake", "keep-awake"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-keep-awake"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/keep-awake/", "dependencies": {}, "devDependencies": {"@testing-library/react-native": "^12.5.2", "expo-module-scripts": "^4.0.4"}, "peerDependencies": {"expo": "*", "react": "*"}, "jest": {"preset": "expo-module-scripts"}, "gitHead": "c01c449a1d6e6e8690bfcc88a778b46781a59674"}