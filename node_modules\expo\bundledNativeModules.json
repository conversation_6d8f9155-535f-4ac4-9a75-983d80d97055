{"@expo/metro-runtime": "~4.0.0-preview.2", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-masked-view/masked-view": "0.3.1", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "4.5.5", "@react-native-community/viewpager": "5.0.11", "@react-native-picker/picker": "2.9.0", "@react-native-segmented-control/segmented-control": "2.5.4", "@stripe/stripe-react-native": "0.38.6", "eslint-config-expo": "~8.0.1", "expo-analytics-amplitude": "~11.3.0", "expo-app-auth": "~11.1.0", "expo-app-loader-provider": "~8.0.0", "expo-apple-authentication": "~7.0.1", "expo-application": "~6.0.1", "expo-asset": "~11.0.1", "expo-audio": "~0.2.3", "expo-auth-session": "~6.0.0", "expo-av": "~15.0.1", "expo-background-fetch": "~13.0.2", "expo-battery": "~9.0.1", "expo-blur": "~14.0.1", "expo-brightness": "~13.0.2", "expo-build-properties": "~0.13.1", "expo-calendar": "~14.0.2", "expo-camera": "~16.0.3", "expo-cellular": "~7.0.1", "expo-checkbox": "~4.0.0", "expo-clipboard": "~7.0.0", "expo-constants": "~17.0.2", "expo-contacts": "~14.0.2", "expo-crypto": "~14.0.1", "expo-dev-client": "~5.0.0-preview.9", "expo-device": "~7.0.1", "expo-document-picker": "~13.0.1", "expo-face-detector": "~13.0.1", "expo-file-system": "~18.0.1", "expo-font": "~13.0.1", "expo-gl": "~15.0.1", "expo-google-app-auth": "~8.3.0", "expo-haptics": "~14.0.0", "expo-image": "~2.0.0-preview.1", "expo-image-loader": "~5.0.0", "expo-image-manipulator": "~13.0.5", "expo-image-picker": "~16.0.1", "expo-intent-launcher": "~12.0.1", "expo-insights": "~0.8.1", "expo-keep-awake": "~14.0.1", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.2", "expo-local-authentication": "~15.0.1", "expo-localization": "~16.0.0", "expo-location": "~18.0.1", "expo-mail-composer": "~14.0.1", "expo-media-library": "~17.0.2", "expo-module-template": "~10.15.6", "expo-modules-core": "~2.0.0", "expo-navigation-bar": "~4.0.2", "expo-network": "~7.0.0", "expo-notifications": "~0.29.6", "expo-print": "~14.0.2", "expo-router": "~4.0.0", "expo-screen-capture": "~7.0.0", "expo-screen-orientation": "~8.0.0", "expo-secure-store": "~14.0.0", "expo-sensors": "~14.0.1", "expo-sharing": "~13.0.0", "expo-sms": "~13.0.0", "expo-speech": "~13.0.0", "expo-splash-screen": "~0.29.5", "expo-sqlite": "~15.0.2", "expo-status-bar": "~2.0.0", "expo-store-review": "~8.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.2", "expo-task-manager": "~12.0.2", "expo-tracking-transparency": "~5.0.0", "expo-updates": "~0.26.5", "expo-video-thumbnails": "~9.0.0", "expo-video": "~2.0.0-preview.2", "expo-web-browser": "~14.0.0", "jest-expo": "~52.0.0-preview.4", "lottie-react-native": "7.0.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.1", "react-native-web": "~0.19.13", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "~1.11.0", "react-native-maps": "1.18.0", "react-native-pager-view": "6.4.1", "react-native-reanimated": "~3.16.1", "react-native-screens": "4.0.0", "react-native-safe-area-context": "4.12.0", "react-native-svg": "15.8.0", "react-native-view-shot": "~4.0.0", "react-native-webview": "13.12.2", "sentry-expo": "~7.0.0", "unimodules-app-loader": "~5.0.0", "unimodules-image-loader-interface": "~6.1.0", "@shopify/react-native-skia": "1.5.0", "@shopify/flash-list": "1.7.1", "@sentry/react-native": "~6.1.0"}