"""
Complete Job Processing Engine for Jobbify Backend
Includes: Quality scoring, Advanced filtering, Personalized search, and Job enhancement utilities
"""

import re
import asyncio
import httpx
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import json
import logging

# Import dependencies
from supabase_client import supabase
from job_apis import jooble, map_jooble_job, muse, map_muse_job, ashby, map_ashby_job
from job_service import adzuna, arbeitnow

# Import API tracking
from api_usage import (
    call_with_tracking,
    APILimitReached,
    get_api_health_status,
    call_jooble_with_tracking,
    call_adzuna_with_tracking,
    call_muse_with_tracking,
    call_arbeitnow_with_tracking
)

# Import metrics
from metrics import (
    track_endpoint_performance,
    record_job_quality,
    record_user_search,
    track_response_time
)

logger = logging.getLogger(__name__)

# ============================================================================
# MODELS AND CONFIGURATION
# ============================================================================

class PersonalizedSearchRequest(BaseModel):
    user_id: str
    force_refresh: bool = False
    limit: int = 100

class APIResponse(BaseModel):
    message: str
    jobs: List[Dict[str, Any]]
    cache_used: bool = False
    api_calls_made: int = 0
    quality_distribution: Optional[Dict[str, Any]] = None

# Quality thresholds for filtering
QUALITY_THRESHOLDS = {
    "excellent": 0.8,
    "good": 0.6,
    "fair": 0.4,
    "poor": 0.2
}

# ============================================================================
# QUALITY SCORING FUNCTIONS
# ============================================================================

def calculate_days_old(job: Dict[str, Any]) -> int:
    """Calculate how many days old a job posting is"""
    try:
        # Try different date fields that might exist
        date_fields = ['datePosted', 'posted_date', 'created_at', 'published_date', 'date']
        
        for field in date_fields:
            if field in job and job[field]:
                date_str = str(job[field])
                
                # Try parsing different date formats
                date_formats = [
                    '%Y-%m-%d',
                    '%Y-%m-%dT%H:%M:%S',
                    '%Y-%m-%dT%H:%M:%SZ',
                    '%Y-%m-%dT%H:%M:%S.%fZ',
                    '%m/%d/%Y',
                    '%d/%m/%Y'
                ]
                
                for fmt in date_formats:
                    try:
                        posted_date = datetime.strptime(date_str[:len(fmt)], fmt)
                        days_old = (datetime.now() - posted_date).days
                        return max(0, days_old)
                    except ValueError:
                        continue
        
        # If no valid date found, assume it's recent (0-7 days old)
        return 3  # Default to 3 days old
        
    except Exception as e:
        logger.debug(f"Error calculating job age: {e}")
        return 3  # Default fallback

def extract_salary_amount(salary_str: str) -> float:
    """Extract numeric salary amount from salary string"""
    if not salary_str:
        return 0
    
    # Remove common currency symbols and text
    cleaned = re.sub(r'[^\d.,k]', '', salary_str.lower())
    
    # Extract numbers
    numbers = re.findall(r'\d+(?:[.,]\d+)?', cleaned)
    
    if numbers:
        try:
            amount = float(numbers[0].replace(',', ''))
            # Handle 'k' notation (e.g., "50k" = 50000)
            if 'k' in salary_str.lower():
                amount *= 1000
            return amount
        except ValueError:
            pass
    
    return 0

def has_company_logo(job: Dict[str, Any]) -> bool:
    """Check if job has a company logo"""
    logo_fields = ['company_logo', 'companyLogo', 'logo', 'company_logo_url', 'logoUrl']
    
    for field in logo_fields:
        if field in job and job[field]:
            logo_value = job[field]
            if isinstance(logo_value, str) and len(logo_value.strip()) > 0:
                # Check if it's not a placeholder or default logo
                if not any(placeholder in logo_value.lower() for placeholder in 
                          ['placeholder', 'default', 'no-logo', 'missing']):
                    return True
    
    return False

def calculate_description_quality(description: str) -> float:
    """Calculate description quality based on length and content"""
    if not description:
        return 0
    
    # Base score on length (longer descriptions are generally better)
    length_score = min(len(description) / 2000, 1.0)  # Cap at 2000 chars
    
    # Bonus for structured content
    structure_bonus = 0
    if any(keyword in description.lower() for keyword in 
           ['responsibilities', 'requirements', 'qualifications', 'benefits']):
        structure_bonus = 0.1
    
    # Penalty for very short descriptions
    if len(description) < 100:
        length_score *= 0.5
    
    return min(length_score + structure_bonus, 1.0)

def score(job: Dict[str, Any]) -> float:
    """
    Calculate overall job quality score (0.0 to 1.0)
    
    Scoring factors:
    - Freshness (50%): Prefer jobs < 30 days old
    - Salary presence (20%): Bonus for having salary info
    - Company logo (10%): Bonus for having company logo
    - Description quality (20%): Based on length and structure
    """
    try:
        # 1. Freshness score (0.5 weight)
        days_old = job.get('daysOld') or calculate_days_old(job)
        freshness_score = max(0, (30 - days_old) / 30)  # Linear decay over 30 days
        
        # 2. Salary bonus (0.2 weight)
        salary_bonus = 0
        salary_fields = ['salary', 'salaryMin', 'salaryMax', 'compensation']
        for field in salary_fields:
            if field in job and job[field]:
                salary_amount = extract_salary_amount(str(job[field]))
                if salary_amount > 0:
                    salary_bonus = 0.2
                    break
        
        # 3. Logo bonus (0.1 weight)
        logo_bonus = 0.1 if has_company_logo(job) else 0
        
        # 4. Description quality (0.2 weight)
        description = job.get('description', '') or job.get('summary', '')
        description_score = calculate_description_quality(description) * 0.2
        
        # Combine all factors
        total_score = (
            freshness_score * 0.5 +
            salary_bonus +
            logo_bonus +
            description_score
        )
        
        # Ensure score is between 0 and 1
        final_score = max(0.0, min(1.0, total_score))
        
        logger.debug(f"Job quality score: {final_score:.3f} "
                    f"(freshness: {freshness_score:.2f}, "
                    f"salary: {salary_bonus}, "
                    f"logo: {logo_bonus}, "
                    f"description: {description_score:.2f})")
        
        return round(final_score, 3)
        
    except Exception as e:
        logger.error(f"Error calculating job quality score: {e}")
        return 0.5  # Default middle score

def score_and_sort_jobs(jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Add quality scores to jobs and sort by quality + relevance
    """
    try:
        # Add quality scores
        for job in jobs:
            job['qualityScore'] = score(job)
        
        # Sort by quality score (primary) and relevance score (secondary)
        jobs.sort(key=lambda j: (
            -j.get('qualityScore', 0),
            -j.get('relevanceScore', 0)
        ))
        
        logger.info(f"Scored and sorted {len(jobs)} jobs by quality")
        return jobs
        
    except Exception as e:
        logger.error(f"Error scoring and sorting jobs: {e}")
        return jobs

def get_quality_distribution(jobs: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Get quality score distribution for monitoring"""
    if not jobs:
        return {"count": 0, "average": 0, "distribution": {}}
    
    scores = [job.get('qualityScore', 0) for job in jobs]
    
    # Create distribution buckets
    buckets = {
        "0.0-0.2": 0,
        "0.2-0.4": 0, 
        "0.4-0.6": 0,
        "0.6-0.8": 0,
        "0.8-1.0": 0
    }
    
    for score in scores:
        if score < 0.2:
            buckets["0.0-0.2"] += 1
        elif score < 0.4:
            buckets["0.2-0.4"] += 1
        elif score < 0.6:
            buckets["0.4-0.6"] += 1
        elif score < 0.8:
            buckets["0.6-0.8"] += 1
        else:
            buckets["0.8-1.0"] += 1
    
    return {
        "count": len(jobs),
        "average": round(sum(scores) / len(scores), 3),
        "min": round(min(scores), 3),
        "max": round(max(scores), 3),
        "distribution": buckets
    }

def filter_by_quality(jobs: List[Dict[str, Any]], min_quality: str = "fair") -> List[Dict[str, Any]]:
    """Filter jobs by minimum quality threshold"""
    threshold = QUALITY_THRESHOLDS.get(min_quality, 0.4)

    filtered_jobs = [job for job in jobs if job.get('qualityScore', 0) >= threshold]

    logger.info(f"Filtered {len(jobs)} jobs to {len(filtered_jobs)} with min quality '{min_quality}' ({threshold})")

    return filtered_jobs

# ============================================================================
# JOB FILTERING FUNCTIONS
# ============================================================================

def has_valid_description(job: Dict[str, Any]) -> bool:
    """Check if job has a valid description for display"""
    description_fields = ['description', 'summary', 'content', 'details']

    for field in description_fields:
        desc = job.get(field)
        if desc and isinstance(desc, str) and len(desc.strip()) >= 500:
            return True

    return False

def has_valid_picture(job: Dict[str, Any]) -> bool:
    """Check if job has a valid picture/logo for display"""
    image_fields = ['logo', 'image', 'company_logo', 'picture']

    for field in image_fields:
        image_url = job.get(field)
        if image_url and isinstance(image_url, str):
            url = image_url.strip()
            # Check if it's NOT a generated/placeholder logo
            if (url.startswith(('http://', 'https://')) and
                not any(placeholder in url for placeholder in [
                    'ui-avatars.com', 'placeholder', 'avatar', 'unsplash.com'
                ])):
                return True

    return False

def has_real_logo(job: Dict[str, Any]) -> bool:
    """
    Check if job has a real company logo (not generated).

    Args:
        job: Job dictionary

    Returns:
        bool: True if has real logo
    """
    image_fields = ['logo', 'image', 'company_logo', 'picture']

    for field in image_fields:
        image_url = job.get(field)
        if image_url and isinstance(image_url, str):
            url = image_url.strip()
            # Check if it's NOT a generated/placeholder logo
            if (url.startswith(('http://', 'https://')) and
                not any(placeholder in url for placeholder in [
                    'ui-avatars.com', 'placeholder', 'avatar', 'unsplash.com'
                ])):
                return True

    return False

def enhance_job_for_display(job: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhance job with missing fields for display

    Args:
        job: Job dictionary

    Returns:
        Enhanced job dictionary
    """
    enhanced_job = job.copy()

    # Enhance description if missing or too short
    if not has_valid_description(enhanced_job):
        company = enhanced_job.get('company', 'Company')
        title = enhanced_job.get('title', 'Position')

        enhanced_description = f"""
Join {company} as a {title}. This is an exciting opportunity to work with a dynamic team and contribute to meaningful projects.

Key Responsibilities:
• Collaborate with cross-functional teams to deliver high-quality results
• Contribute to innovative projects and solutions
• Develop and maintain professional relationships with colleagues
• Participate in continuous learning and professional development

Requirements:
• Strong communication and interpersonal skills
• Ability to work independently and as part of a team
• Problem-solving mindset and attention to detail
• Relevant experience or education in the field

What We Offer:
• Competitive compensation package
• Professional development opportunities
• Collaborative and inclusive work environment
• Opportunity to make a meaningful impact

We're looking for talented individuals who are passionate about their work and eager to contribute to our team's success. If you're ready to take the next step in your career, we'd love to hear from you.
        """.strip()

        enhanced_job['description'] = enhanced_description

    # Enhance logo if missing
    if not has_valid_picture(enhanced_job):
        company = enhanced_job.get('company', 'Company')
        # Generate a fallback logo
        enhanced_job['logo'] = f"https://ui-avatars.com/api/?name={company.replace(' ', '+')}&background=random&size=150"

    return enhanced_job

def filter_jobs_for_display(jobs: List[Dict[str, Any]],
                           strict_mode: bool = False,
                           enhance_missing: bool = True,
                           prioritize_real_logos: bool = True) -> List[Dict[str, Any]]:
    """
    Filter jobs to ensure they have description and picture for display.

    Args:
        jobs: List of job dictionaries
        strict_mode: If True, exclude jobs without valid description/picture
                    If False, enhance jobs with missing fields
        enhance_missing: If True, add fallback values for missing fields
        prioritize_real_logos: If True, prioritize jobs with real company logos

    Returns:
        List of jobs suitable for display
    """
    if not jobs:
        return []

    filtered_jobs = []
    stats = {
        'total': len(jobs),
        'valid_description': 0,
        'valid_picture': 0,
        'both_valid': 0,
        'enhanced': 0,
        'filtered_out': 0
    }

    for job in jobs:
        has_desc = has_valid_description(job)
        has_pic = has_valid_picture(job)

        # Update stats
        if has_desc:
            stats['valid_description'] += 1
        if has_pic:
            stats['valid_picture'] += 1
        if has_desc and has_pic:
            stats['both_valid'] += 1

        if strict_mode:
            # Strict mode: only include jobs with both description and picture
            if has_desc and has_pic:
                filtered_jobs.append(job)
            else:
                stats['filtered_out'] += 1
        else:
            # Lenient mode: enhance jobs with missing fields
            if enhance_missing:
                enhanced_job = enhance_job_for_display(job)
                filtered_jobs.append(enhanced_job)
                if not has_desc or not has_pic:
                    stats['enhanced'] += 1
            elif has_desc and has_pic:
                filtered_jobs.append(job)
            else:
                stats['filtered_out'] += 1

    # Prioritize jobs with real logos if requested
    if prioritize_real_logos:
        filtered_jobs.sort(key=lambda j: has_real_logo(j), reverse=True)

    logger.info(f"Job filtering stats: {stats}")
    return filtered_jobs

# ============================================================================
# PERSONALIZED SEARCH FUNCTIONS
# ============================================================================

def remove_duplicates(jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Remove duplicate jobs based on title and company"""
    seen = set()
    unique_jobs = []

    for job in jobs:
        # Create a key based on title and company
        title = job.get('title', '').lower().strip()
        company = job.get('company', '').lower().strip()
        key = f"{title}|{company}"

        if key not in seen:
            seen.add(key)
            unique_jobs.append(job)

    logger.info(f"Removed {len(jobs) - len(unique_jobs)} duplicate jobs")
    return unique_jobs

def filter_and_rank_jobs(jobs: List[Dict[str, Any]], user_preferences: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Filter and rank jobs based on user preferences"""
    filtered_jobs = []

    # Get user preferences
    preferred_locations = user_preferences.get('preferred_locations', [])
    preferred_job_types = user_preferences.get('preferred_job_types', [])
    experience_level = user_preferences.get('experience_level', '')
    min_salary = user_preferences.get('min_salary', 0)
    max_salary = user_preferences.get('max_salary', 1000000)

    for job in jobs:
        score = 0

        # Location matching
        job_location = job.get('location', '').lower()
        for pref_location in preferred_locations:
            if pref_location.lower() in job_location:
                score += 20
                break

        # Job type matching
        job_title = job.get('title', '').lower()
        for job_type in preferred_job_types:
            if job_type.lower() in job_title:
                score += 15
                break

        # Experience level matching
        if experience_level:
            job_desc = job.get('description', '').lower()
            if experience_level.lower() in job_desc:
                score += 10

        # Salary matching
        job_salary = job.get('salary', '')
        if job_salary:
            # Simple salary extraction (this could be improved)
            salary_numbers = re.findall(r'\d+', str(job_salary))
            if salary_numbers:
                try:
                    salary = int(salary_numbers[0])
                    if salary >= min_salary and salary <= max_salary:
                        score += 5
                except ValueError:
                    pass

        # API source bonus (prioritize certain sources)
        api_source = job.get('api_source', '')
        if api_source == 'jooble':
            score += 10  # Jooble often has good job descriptions
        elif api_source == 'muse':
            score += 8   # The Muse has quality jobs

        job['relevance_score'] = score
        filtered_jobs.append(job)

    # Sort by relevance score and posting date
    return sorted(filtered_jobs, key=lambda x: (x.get('relevance_score', 0), x.get('posted_date', '')), reverse=True)
