"""
Health check endpoints and system monitoring
"""

import asyncio
import time
import logging
from typing import Dict, Any, List
from datetime import datetime
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from config import get_settings
from supabase_client import supabase, db_manager
# Cache removed - using direct Supabase storage

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/health", tags=["health"])

settings = get_settings()


async def check_database_health() -> Dict[str, Any]:
    """Check database connectivity and performance"""
    start_time = time.time()
    
    try:
        # Simple query to test connection
        result = db_manager.safe_select("jobs", "id", limit=1)
        
        duration = (time.time() - start_time) * 1000
        
        if result.get("error"):
            return {
                "status": "unhealthy",
                "error": result["error"],
                "response_time_ms": duration
            }
        
        return {
            "status": "healthy",
            "response_time_ms": duration,
            "connection_pool_size": settings.db_pool_size
        }
    
    except Exception as e:
        duration = (time.time() - start_time) * 1000
        return {
            "status": "unhealthy",
            "error": str(e),
            "response_time_ms": duration
        }


async def check_cache_health() -> Dict[str, Any]:
    """Cache health check - cache removed, using direct Supabase storage"""
    return {
        "status": "disabled",
        "message": "Cache removed - using direct Supabase storage",
        "response_time_ms": 0
    }


async def check_external_apis() -> Dict[str, Any]:
    """Check external API availability"""
    import httpx
    
    apis_to_check = [
        {"name": "jooble", "url": "https://jooble.org/api/ping", "timeout": 5},
        {"name": "themuse", "url": "https://www.themuse.com/api/public/ping", "timeout": 5},
    ]
    
    results = {}
    
    async with httpx.AsyncClient() as client:
        for api in apis_to_check:
            start_time = time.time()
            try:
                response = await client.get(
                    api["url"], 
                    timeout=api["timeout"],
                    headers={"User-Agent": "Jobbify-HealthCheck/1.0"}
                )
                duration = (time.time() - start_time) * 1000
                
                results[api["name"]] = {
                    "status": "healthy" if response.status_code < 400 else "unhealthy",
                    "status_code": response.status_code,
                    "response_time_ms": duration
                }
            
            except Exception as e:
                duration = (time.time() - start_time) * 1000
                results[api["name"]] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "response_time_ms": duration
                }
    
    return results


@router.get("/")
async def basic_health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "service": "jobbify-api"
    }


@router.get("/detailed")
async def detailed_health_check():
    """Detailed health check with all system components"""
    start_time = time.time()
    
    # Run all health checks concurrently
    database_health, cache_health, api_health = await asyncio.gather(
        check_database_health(),
        check_cache_health(),
        check_external_apis(),
        return_exceptions=True
    )
    
    total_duration = (time.time() - start_time) * 1000
    
    # Determine overall status
    overall_status = "healthy"
    
    if isinstance(database_health, Exception) or database_health.get("status") != "healthy":
        overall_status = "unhealthy"
    
    if isinstance(cache_health, Exception) or cache_health.get("status") not in ["healthy", "disconnected"]:
        overall_status = "degraded"
    
    # Check if any external APIs are down
    if isinstance(api_health, dict):
        unhealthy_apis = [name for name, status in api_health.items() if status.get("status") != "healthy"]
        if unhealthy_apis and overall_status == "healthy":
            overall_status = "degraded"
    
    health_report = {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "total_check_time_ms": total_duration,
        "components": {
            "database": database_health if not isinstance(database_health, Exception) else {"status": "error", "error": str(database_health)},
            "cache": cache_health if not isinstance(cache_health, Exception) else {"status": "error", "error": str(cache_health)},
            "external_apis": api_health if not isinstance(api_health, Exception) else {"status": "error", "error": str(api_health)}
        },
        "system_info": {
            "environment": "production" if not settings.debug else "development",
            "log_level": settings.log_level,
            "cache_enabled": cache.connected,
            "prometheus_enabled": settings.prometheus_enabled
        }
    }
    
    # Return appropriate HTTP status code
    if overall_status == "unhealthy":
        return JSONResponse(status_code=503, content=health_report)
    elif overall_status == "degraded":
        return JSONResponse(status_code=200, content=health_report)
    else:
        return JSONResponse(status_code=200, content=health_report)


@router.get("/readiness")
async def readiness_check():
    """Kubernetes readiness probe endpoint"""
    try:
        # Check critical dependencies
        db_health = await check_database_health()
        
        if db_health.get("status") != "healthy":
            return JSONResponse(
                status_code=503,
                content={"status": "not_ready", "reason": "database_unavailable"}
            )
        
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
    
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={"status": "not_ready", "reason": str(e)}
        )


@router.get("/liveness")
async def liveness_check():
    """Kubernetes liveness probe endpoint"""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": time.time() - getattr(liveness_check, 'start_time', time.time())
    }


# Initialize start time for uptime calculation
liveness_check.start_time = time.time()
