=src/main/kotlin/expo/modules/plugin/AutolinkingIntegration.kt>src/main/kotlin/expo/modules/plugin/ExpoModulesGradlePlugin.kt?src/main/kotlin/expo/modules/plugin/ExtraPropertiesExtension.kt;src/main/kotlin/expo/modules/plugin/ProjectConfiguration.kt.src/main/kotlin/expo/modules/plugin/Version.kt/src/main/kotlin/expo/modules/plugin/Warnings.ktFsrc/main/kotlin/expo/modules/plugin/android/AndroidLibraryExtension.ktHsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktGsrc/main/kotlin/expo/modules/plugin/gradle/ExpoGradleHelperExtension.ktAsrc/main/kotlin/expo/modules/plugin/gradle/ExpoModuleExtension.ktRsrc/withAutolinkingPlugin/kotlin/expo/modules/plugin/AutolinkingIntegrationImpl.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           